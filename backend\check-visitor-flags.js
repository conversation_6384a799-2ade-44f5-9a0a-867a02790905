require('dotenv').config();
const mongoose = require('mongoose');
const { batchGetCountriesFromIPs } = require('./utils/geolocation');

const visitSchema = new mongoose.Schema({
  ip: { type: String, required: true },
  section: { type: String, required: true },
  duration: { type: Number, default: 0 },
  timestamp: { type: Date, default: Date.now },
  sessionId: String,
  pageUrl: String,
  userAgent: String,
  referrer: String,
  jobTitle: String,
  jobSlug: String,
  projectTitle: String,
  interactionType: String
});

const Visit = mongoose.model('Visit', visitSchema);

async function checkVisitorFlags() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');
    
    console.log('\n📊 Fetching recent visitor data...');
    const visitors = await Visit.aggregate([
      {
        $group: {
          _id: '$ip',
          visitCount: { $sum: 1 },
          totalDuration: { $sum: '$duration' },
          lastVisit: { $max: '$timestamp' },
          sections: { $addToSet: '$section' }
        }
      },
      { $sort: { lastVisit: -1 } },
      { $limit: 15 }
    ]);
    
    console.log(`\n🔍 Found ${visitors.length} recent visitors:`);
    visitors.forEach((visitor, index) => {
      console.log(`${index + 1}. ${visitor._id} - ${visitor.visitCount} visits`);
    });
    
    console.log('\n🧪 Testing geolocation for visitor IPs...');
    const ips = visitors.map(v => v._id);
    const geoData = await batchGetCountriesFromIPs(ips);
    
    console.log('\n🏁 Flag display results:');
    console.log('========================');
    
    let successCount = 0;
    let unknownCount = 0;
    
    Object.entries(geoData).forEach(([ip, geo]) => {
      const status = geo.error ? '❌' : '✅';
      const flag = geo.flag || '🌍';
      const country = geo.country || 'Unknown';
      
      if (!geo.error) successCount++;
      if (country === 'Unknown') unknownCount++;
      
      console.log(`${status} ${ip.padEnd(15)} → ${country.padEnd(12)} ${flag}`);
    });
    
    console.log('\n📈 Summary:');
    console.log(`✅ Successful: ${successCount}/${ips.length}`);
    console.log(`❌ Unknown: ${unknownCount}/${ips.length}`);
    console.log(`📊 Success rate: ${Math.round((successCount / ips.length) * 100)}%`);
    
    if (unknownCount === 0) {
      console.log('\n🎉 All visitor IPs now have proper geolocation and flags!');
    } else {
      console.log(`\n⚠️ ${unknownCount} IPs still showing as unknown - may need additional IP ranges`);
    }
    
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

checkVisitorFlags();
